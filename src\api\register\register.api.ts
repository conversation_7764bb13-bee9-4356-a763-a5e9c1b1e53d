import { APIRequestContext, APIResponse } from '@playwright/test';
import { ENDPOINTS } from '@core/const/endpoint';
import { RegisterUserRequest } from '../../data-type/register-user.type';

export default class RegisterAPI {
    private request: APIRequestContext;

    constructor(request: APIRequestContext) {
        this.request = request;
    }

    async register(userData: RegisterUserRequest, avatarFile?: Buffer): Promise<APIResponse> {
        const multipartData: Record<string, any> = {
            Email: userData.Email,
            Password: userData.Password,
            FullName: userData.FullName,
            Role: userData.Role.toString()
        };

        // Add boolean fields with proper string conversion
        if (typeof userData.IsNotification === 'boolean') {
            multipartData.IsNotification = userData.IsNotification.toString();
        }

        if (typeof userData.IsReceiveMessage === 'boolean') {
            multipartData.IsReceiveMessage = userData.IsReceiveMessage.toString();
        }

        if (typeof userData.IsPrivateProfile === 'boolean') {
            multipartData.IsPrivateProfile = userData.IsPrivateProfile.toString();
        }

        if (avatarFile) {
            multipartData.AvatarUrl = {
                name: 'avatar.jpg',
                mimeType: 'image/jpeg',
                buffer: avatarFile
            };
        }

        if (userData.Bio) multipartData.Bio = userData.Bio;

        // Handle arrays properly for multipart data
        if (userData.Expertises?.length) {
            userData.Expertises.forEach((expertise, index) => {
                multipartData[`Expertises[${index}]`] = expertise;
            });
        }

        if (userData.ProfessionalSkill) multipartData.ProfessionalSkill = userData.ProfessionalSkill;
        if (userData.Experience) multipartData.Experience = userData.Experience;

        if (typeof userData.CommunicationPreference === 'number') {
            multipartData.CommunicationPreference = userData.CommunicationPreference.toString();
        }

        if (userData.Goals) multipartData.Goals = userData.Goals;

        // Handle Availability array
        if (userData.Availability?.length) {
            userData.Availability.forEach((availability, index) => {
                multipartData[`Availability[${index}]`] = availability.toString();
            });
        }

        // Handle CourseCategoryIds array
        if (userData.CourseCategoryIds?.length) {
            userData.CourseCategoryIds.forEach((categoryId, index) => {
                multipartData[`CourseCategoryIds[${index}]`] = categoryId;
            });
        }

        if (typeof userData.SessionFrequency === 'number') {
            multipartData.SessionFrequency = userData.SessionFrequency.toString();
        }

        if (typeof userData.Duration === 'number') {
            multipartData.Duration = userData.Duration.toString();
        }

        if (typeof userData.LearningStyle === 'number') {
            multipartData.LearningStyle = userData.LearningStyle.toString();
        }

        // Handle TeachingStyles array
        if (userData.TeachingStyles?.length) {
            userData.TeachingStyles.forEach((style, index) => {
                multipartData[`TeachingStyles[${index}]`] = style.toString();
            });
        }

        return await this.request.post(ENDPOINTS.REGISTER, {
            multipart: multipartData
        });
    }
}